/**
 * 基本的 Agent 功能測試
 * 測試 AgentRunnerService 的核心功能
 */

const { Test } = require('@nestjs/testing');

async function testAgentBasicFunctionality() {
  console.log('🚀 開始測試 Agent 基本功能...\n');

  try {
    // 模擬依賴服務
    const mockPrismaService = {
      $queryRaw: jest.fn().mockResolvedValue([{ '?column?': 1 }]),
    };

    const mockAiModelsService = {
      findOne: jest.fn(),
    };

    const mockAiKeysService = {
      findOne: jest.fn(),
    };

    // 動態導入 AgentRunnerService
    const { AgentRunnerService } = await import('./src/modules/agent/agent.service.js');

    console.log('✓ AgentRunnerService 類別載入成功');

    // 創建測試模組
    const moduleRef = await Test.createTestingModule({
      providers: [
        AgentRunnerService,
        {
          provide: 'PrismaService',
          useValue: mockPrismaService,
        },
        {
          provide: 'AiModelsService',
          useValue: mockAiModelsService,
        },
        {
          provide: 'AiKeysService',
          useValue: mockAiKeysService,
        },
      ],
    }).compile();

    const agentService = moduleRef.get(AgentRunnerService);
    console.log('✓ AgentRunnerService 實例創建成功');

    // 測試基本功能
    console.log('\n📋 測試基本功能:');

    // 1. 測試 getAgentStatus
    console.log('1. 測試 getAgentStatus...');
    const status = await agentService.getAgentStatus();
    console.log(`   狀態: ${status.status}`);
    console.log(`   訊息: ${status.message}`);
    console.log('   ✓ getAgentStatus 測試通過');

    // 2. 測試 runAgent 正常情況
    console.log('\n2. 測試 runAgent 正常情況...');
    const result = await agentService.runAgent(
      'Hello, how can you help me?',
      'test-tenant-123'
    );
    console.log(`   結果長度: ${result.length} 字符`);
    console.log(`   結果預覽: ${result.substring(0, 100)}...`);
    console.log('   ✓ runAgent 正常情況測試通過');

    // 3. 測試 runAgent 帶配置 ID
    console.log('\n3. 測試 runAgent 帶配置 ID...');
    const resultWithConfig = await agentService.runAgent(
      'What is the weather today?',
      'test-tenant-123',
      'custom-config-id'
    );
    console.log(`   結果長度: ${resultWithConfig.length} 字符`);
    console.log('   ✓ runAgent 帶配置 ID 測試通過');

    // 4. 測試錯誤處理
    console.log('\n4. 測試錯誤處理...');
    try {
      await agentService.runAgent('', 'test-tenant-123');
      console.log('   ✗ 應該拋出錯誤但沒有');
      return false;
    } catch (error) {
      console.log(`   ✓ 正確拋出錯誤: ${error.message}`);
    }

    try {
      await agentService.runAgent('Hello', '');
      console.log('   ✗ 應該拋出錯誤但沒有');
      return false;
    } catch (error) {
      console.log(`   ✓ 正確拋出錯誤: ${error.message}`);
    }

    console.log('\n🎉 所有基本功能測試通過！');
    return true;

  } catch (error) {
    console.error('\n❌ 測試失敗:', error.message);
    console.error('錯誤堆疊:', error.stack);
    return false;
  }
}

// 設置 Jest mock
global.jest = {
  fn: () => ({
    mockResolvedValue: (value) => () => Promise.resolve(value),
    mockRejectedValue: (error) => () => Promise.reject(error),
  })
};

// 執行測試
testAgentBasicFunctionality()
  .then(success => {
    if (success) {
      console.log('\n✅ Agent 模組基本功能驗證完成！');
      process.exit(0);
    } else {
      console.log('\n❌ 測試失敗！');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('\n❌ 測試執行錯誤:', error);
    process.exit(1);
  });
